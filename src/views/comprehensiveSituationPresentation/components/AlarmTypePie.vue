<template>
  <div class="alarm-type-pie-container">
    <div class="hot-apps-title">
      <div class="hot-apps-icon">
        <div></div>
        <div style="margin-left: 3px"></div>
      </div>
      <span class="hot-apps-text">告警概述</span>
    </div>
    <div ref="chartRef" class="alarm-type-pie"></div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
  name: "AlarmTypePie"
});
</script>

<script setup lang="ts">
import { ref, onMounted, watch, onUnmounted } from "vue";
import * as echarts from "echarts/core";
import { PieChart } from "echarts/charts";
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent
} from "echarts/components";
import { CanvasRenderer } from "echarts/renderers";
import alarmTypeTitleImg from "../assets/CPU利用率趋势图-标题@3x(5).png";

// 注册必要的组件
echarts.use([
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  CanvasRenderer
]);

// 定义图表数据类型
interface AlarmTypeItem {
  name: string;
  value: number;
  color: string;
}

const props = defineProps<{
  data: AlarmTypeItem[];
}>();

const chartRef = ref<HTMLElement | null>(null);
let chart: echarts.ECharts | null = null;

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;

  chart = echarts.init(chartRef.value);

  const option = {
    backgroundColor: "transparent",
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)",
      backgroundColor: "rgba(255, 255, 255, 0.95)",
      borderColor: "#e4e7ed",
      textStyle: {
        color: "#333333"
      }
    },
    legend: {
      orient: "vertical",
      right: "0%",
      top: "middle",
      itemWidth: 10,
      itemHeight: 10,
      icon: "circle",
      itemGap: 16,
      textStyle: {
        color: "#2c3e50",
        fontSize: 14
      }
    },
    series: [
      {
        name: "告警类型",
        type: "pie",
        radius: ["25%", "70%"],
        center: ["32%", "50%"],
        startAngle: 200,
        avoidLabelOverlap: false,
        roseType: "radius",
        itemStyle: {
          borderRadius: 0,
          borderColor: "#ffffff",
          borderWidth: 2
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: false
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)"
          }
        },
        labelLine: {
          show: false
        },
        data: props.data.map(item => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: item.color
          }
        }))
      }
    ]
  };

  chart.setOption(option);
};

// 更新图表
const updateChart = () => {
  if (!chart) return;

  chart.setOption({
    series: [
      {
        data: props.data.map(item => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: item.color
          }
        }))
      }
    ]
  });
};

// 监听窗口大小变化，调整图表大小
const handleResize = () => {
  chart && chart.resize();
};

// 监听数据变化，更新图表
watch(
  () => props.data,
  () => {
    if (chart) {
      updateChart();
    } else {
      initChart();
    }
  },
  { deep: true }
);

// 组件挂载时初始化图表
onMounted(() => {
  initChart();
  window.addEventListener("resize", handleResize);
});

// 组件卸载时清理图表实例
onUnmounted(() => {
  if (chart) {
    chart.dispose();
    chart = null;
  }
  window.removeEventListener("resize", handleResize);
});
</script>

<style lang="scss" scoped>
.alarm-type-pie-container {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  box-sizing: border-box;
  border: 1px solid #e4e7ed;
  .hot-apps-title {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .hot-apps-icon {
      font-size: 16px;
      margin-right: 6px;
      display: flex;
      div {
        height: 13px;
        width: 3px;
        background: #3296fb;
        border-radius: 3px;
        transform: rotate(16deg);
      }
    }

    .hot-apps-text {
      font-size: 14px;
      font-weight: 600;
      color: #000000db;
    }
  }
  .alarm-title {
    margin-bottom: 5px;

    .title-image {
      position: absolute;
      top: -33px;
      left: -4px;
      height: 63px;
      object-fit: contain;
      margin-top: 5px;
      margin-bottom: 5px;
    }
  }

  .alarm-type-pie {
    width: 100%;
    flex: 1;
    min-height: 180px;
    margin-top: 10px;
  }

  :deep(.el-tooltip__trigger) {
    display: flex;
    align-items: center;
  }
}
</style>
